buildscript {
    ext.kotlin_version = '1.8.0'
    repositories {
        google()
        mavenCentral()

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
