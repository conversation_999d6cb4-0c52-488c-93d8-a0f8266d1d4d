@echo off
echo Compilando SKECH FiveM Cheat Menu...

@REM Setup Visual Studio environment
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
) else (
    echo Visual Studio nao encontrado!
    pause
    exit /b 1
)

@set OUT_DIR=Debug
@set OUT_EXE=SkechFiveMCheat
@set INCLUDES=/I..\.. /I..\..\backends
@set SOURCES=main.cpp nav_elements.cpp etc_elements.cpp ..\..\backends\imgui_impl_dx9.cpp ..\..\backends\imgui_impl_win32.cpp ..\..\imgui*.cpp
@set LIBS=d3d9.lib user32.lib gdi32.lib

if not exist %OUT_DIR% mkdir %OUT_DIR%

echo Compilando...
cl /nologo /Zi /MD /std:c++17 %INCLUDES% /D UNICODE /D _UNICODE %SOURCES% /Fe%OUT_DIR%/%OUT_EXE%.exe /Fo%OUT_DIR%/ /link %LIBS%

if %ERRORLEVEL% == 0 (
    echo.
    echo ========================================
    echo Compilacao concluida com sucesso!
    echo Executavel criado em: %OUT_DIR%\%OUT_EXE%.exe
    echo ========================================
    echo.
    echo Iniciando o menu...
    cd %OUT_DIR%
    start %OUT_EXE%.exe
    echo.
    echo Menu iniciado! Verifique a janela que abriu.
    echo.
) else (
    echo.
    echo ========================================
    echo Erro na compilacao!
    echo Verifique os erros acima.
    echo ========================================
)

pause
