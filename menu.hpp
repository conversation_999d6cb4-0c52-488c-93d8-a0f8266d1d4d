#pragma once
#include <imgui.h>
#include <string>
#include <vector>
#include <functional>

// Menu configuration and styling
namespace MenuConfig {
    // Window dimensions
    constexpr float WINDOW_WIDTH = 849.0f;
    constexpr float WINDOW_HEIGHT = 536.0f;
    constexpr float SIDEBAR_WIDTH = 270.0f;
    
    // Colors (based on your design)
    constexpr ImVec4 BACKGROUND_COLOR = ImVec4(0.059f, 0.063f, 0.063f, 1.0f);  // #0F1010
    constexpr ImVec4 ACCENT_COLOR = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);           // #FF0000
    constexpr ImVec4 PANEL_COLOR = ImVec4(0.067f, 0.071f, 0.071f, 1.0f);      // #111212
    constexpr ImVec4 HEADER_COLOR = ImVec4(0.063f, 0.067f, 0.067f, 1.0f);     // #101111
    constexpr ImVec4 TEXT_COLOR = ImVec4(1.0f, 1.0f, 1.0f, 0.58f);
    constexpr ImVec4 TEXT_INACTIVE = ImVec4(1.0f, 1.0f, 1.0f, 0.14f);
    constexpr ImVec4 TEXT_ACTIVE = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    constexpr ImVec4 BORDER_COLOR = ImVec4(1.0f, 1.0f, 1.0f, 0.15f);
}

// Menu categories
enum class MenuCategory {
    AIM = 0,
    PLAYER,
    VISUALS,
    MISCELLANEOUS,
    COUNT
};

// Aimbot settings structure
struct AimbotSettings {
    bool enabled = false;
    bool silentAim = false;
    bool magicBullet = false;
    bool triggerbot = false;
    bool visibleCheck = false;
    bool useHotkey = false;
    bool dualAimbot = false;
    bool targetPlayers = true;
    bool targetPeds = false;
    bool targetDead = false;
    bool fovCircle = false;
    
    float fieldOfView = 90.0f;
    float smoothing = 1.0f;
    float curve = 1.0f;
    float aimDistance = 100.0f;
    float delay = 0.0f;
    
    int hitbox = 0; // 0=Head, 1=Chest, 2=Body
    int aimbotHotkey = 1; // Mouse button
    int silentAimHotkey = 1;
    int magicBulletHotkey = 1;
    int triggerbotKey = 1;
    
    ImVec4 fovColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
};

// Player settings structure
struct PlayerSettings {
    bool godMode = false;
    bool infiniteAmmo = false;
    bool noRecoil = false;
    bool noSpread = false;
    bool fastReload = false;
    bool superJump = false;
    bool speedHack = false;
    bool noclip = false;
    
    float speedMultiplier = 1.0f;
    float jumpHeight = 1.0f;
};

// Visual settings structure
struct VisualSettings {
    bool esp = false;
    bool playerNames = false;
    bool playerBoxes = false;
    bool playerHealth = false;
    bool playerDistance = false;
    bool vehicleEsp = false;
    bool weaponEsp = false;
    bool crosshair = false;
    bool radar = false;
    
    ImVec4 espColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    ImVec4 enemyColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
    ImVec4 friendColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
    
    float espDistance = 500.0f;
};

// Miscellaneous settings structure
struct MiscSettings {
    bool bunnyHop = false;
    bool autoRun = false;
    bool antiAfk = false;
    bool chatSpam = false;
    bool autoAccept = false;
    
    std::string chatMessage = "Hello World!";
    float chatDelay = 1.0f;
};

// Main menu class
class CheatMenu {
private:
    bool m_isVisible = false;
    MenuCategory m_currentCategory = MenuCategory::AIM;
    
    // Settings structures
    AimbotSettings m_aimbotSettings;
    PlayerSettings m_playerSettings;
    VisualSettings m_visualSettings;
    MiscSettings m_miscSettings;
    
    // UI state
    bool m_showDemo = false;
    float m_menuAlpha = 1.0f;
    
    // Private methods
    void SetupStyle();
    void DrawSidebar();
    void DrawMainContent();
    void DrawAimbotTab();
    void DrawPlayerTab();
    void DrawVisualsTab();
    void DrawMiscellaneousTab();
    void DrawHeader(const char* title);
    void DrawSection(const char* title, std::function<void()> content);
    bool DrawToggle(const char* label, bool* value);
    bool DrawSliderFloat(const char* label, float* value, float min, float max, const char* format = "%.1f");
    bool DrawSliderInt(const char* label, int* value, int min, int max);
    bool DrawCombo(const char* label, int* current_item, const char* const items[], int items_count);
    bool DrawColorEdit(const char* label, ImVec4* color);
    bool DrawInputText(const char* label, std::string* str);
    void DrawHotkeyButton(const char* label, int* hotkey);
    
public:
    CheatMenu();
    ~CheatMenu();
    
    // Main interface
    void Initialize();
    void Render();
    void Shutdown();
    
    // Menu control
    void Show() { m_isVisible = true; }
    void Hide() { m_isVisible = false; }
    void Toggle() { m_isVisible = !m_isVisible; }
    bool IsVisible() const { return m_isVisible; }
    
    // Settings access
    const AimbotSettings& GetAimbotSettings() const { return m_aimbotSettings; }
    const PlayerSettings& GetPlayerSettings() const { return m_playerSettings; }
    const VisualSettings& GetVisualSettings() const { return m_visualSettings; }
    const MiscSettings& GetMiscSettings() const { return m_miscSettings; }
    
    // Settings modification
    AimbotSettings& GetAimbotSettings() { return m_aimbotSettings; }
    PlayerSettings& GetPlayerSettings() { return m_playerSettings; }
    VisualSettings& GetVisualSettings() { return m_visualSettings; }
    MiscSettings& GetMiscSettings() { return m_miscSettings; }
    
    // Configuration
    void LoadConfig(const std::string& filename);
    void SaveConfig(const std::string& filename);
    void ResetToDefaults();
};

// Global menu instance
extern CheatMenu* g_Menu;

// Utility functions
namespace MenuUtils {
    void PushStyleColors();
    void PopStyleColors();
    void DrawGlow(ImVec2 pos, ImVec2 size, ImVec4 color, float intensity = 1.0f);
    void DrawBlurredBackground(ImVec2 pos, ImVec2 size);
    bool IsKeyPressed(int key);
    std::string GetKeyName(int key);
    ImVec2 CalcTextSize(const char* text);
    void SetTooltip(const char* text);
}

// Hotkey definitions
namespace Hotkeys {
    constexpr int MOUSE_LEFT = 0;
    constexpr int MOUSE_RIGHT = 1;
    constexpr int MOUSE_MIDDLE = 2;
    constexpr int KEY_SHIFT = 16;
    constexpr int KEY_CTRL = 17;
    constexpr int KEY_ALT = 18;
    constexpr int KEY_F1 = 112;
    constexpr int KEY_F2 = 113;
    constexpr int KEY_F3 = 114;
    constexpr int KEY_F4 = 115;
    constexpr int KEY_INSERT = 45;
    constexpr int KEY_DELETE = 46;
    constexpr int KEY_HOME = 36;
    constexpr int KEY_END = 35;
}
