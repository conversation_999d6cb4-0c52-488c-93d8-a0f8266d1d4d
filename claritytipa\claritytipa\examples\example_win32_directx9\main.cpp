// Dear ImGui: standalone example application for DirectX 9
// If you are new to Dear ImGui, read documentation from the docs/ folder + read the top of imgui.cpp.
// Read online: https://github.com/ocornut/imgui/tree/master/docs

#include "imgui.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "imgui_internal.h"
#include "imgui_impl_dx9.h"
#include "imgui_impl_win32.h"
#include <d3d9.h>
#include <tchar.h>

#include "byte_array.h"
#include "nav_elements.h"
#include "etc_elements.h"

//namespace fonts
ImFont* medium;
ImFont* bold;
ImFont* tab_icons;
ImFont* logo;
ImFont* tab_title;
ImFont* tab_title_icon;
ImFont* subtab_title;
ImFont* combo_arrow;

enum heads {
    aim, player, visuals, miscellaneous, configs
};

enum aim_sub_heads {
    aimbot, triggerbot, silent_aim, magic_bullet
};

enum player_sub_heads {
    player_general
};

enum visuals_sub_heads {
    players, world
};

enum misc_sub_heads {
    lists, miscellaneous_general
};

// Data
static LPDIRECT3D9              g_pD3D = NULL;
static LPDIRECT3DDEVICE9        g_pd3dDevice = NULL;
static D3DPRESENT_PARAMETERS    g_d3dpp = {};

// Forward declarations of helper functions
bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void ResetDevice();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Main code
int main(int, char**)
{
    // Create application window
    //ImGui_ImplWin32_EnableDpiAwareness();
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(NULL), NULL, NULL, NULL, NULL, _T("ImGui Example"), NULL };
    ::RegisterClassEx(&wc);
    HWND hwnd = ::CreateWindow(wc.lpszClassName, _T("Dear ImGui DirectX9 Example"), WS_OVERLAPPEDWINDOW, 100, 100, 1280, 800, NULL, NULL, wc.hInstance, NULL);

    // Initialize Direct3D
    if (!CreateDeviceD3D(hwnd))
    {
        CleanupDeviceD3D();
        ::UnregisterClass(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    // Show the window
    ::ShowWindow(hwnd, SW_SHOWDEFAULT);
    ::UpdateWindow(hwnd);

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    //io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;     // Enable Keyboard Controls
    //io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;      // Enable Gamepad Controls

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX9_Init(g_pd3dDevice);

    // Load Fonts
    ImFontConfig font_config;
    font_config.PixelSnapH = false;
    font_config.OversampleH = 5;
    font_config.OversampleV = 5;
    font_config.RasterizerMultiply = 1.2f;

    static const ImWchar ranges[] =
    {
        0x0020, 0x00FF, // Basic Latin + Latin Supplement
        0x0400, 0x052F, // Cyrillic + Cyrillic Supplement
        0x2DE0, 0x2DFF, // Cyrillic Extended-A
        0xA640, 0xA69F, // Cyrillic Extended-B
        0xE000, 0xE226, // icons
        0,
    };

    font_config.GlyphRanges = ranges;

    medium = io.Fonts->AddFontFromMemoryTTF(PTRootUIMedium, sizeof(PTRootUIMedium), 15.0f, &font_config, ranges);
    bold = io.Fonts->AddFontFromMemoryTTF(PTRootUIBold, sizeof(PTRootUIBold), 15.0f, &font_config, ranges);

    tab_icons = io.Fonts->AddFontFromMemoryTTF(clarityfont, sizeof(clarityfont), 15.0f, &font_config, ranges);
    logo = io.Fonts->AddFontFromMemoryTTF(clarityfont, sizeof(clarityfont), 21.0f, &font_config, ranges);

    tab_title = io.Fonts->AddFontFromMemoryTTF(PTRootUIBold, sizeof(PTRootUIBold), 19.0f, &font_config, ranges);
    tab_title_icon = io.Fonts->AddFontFromMemoryTTF(clarityfont, sizeof(clarityfont), 18.0f, &font_config, ranges);

    subtab_title = io.Fonts->AddFontFromMemoryTTF(PTRootUIBold, sizeof(PTRootUIBold), 15.0f, &font_config, ranges);

    combo_arrow = io.Fonts->AddFontFromMemoryTTF(combo, sizeof(combo), 9.0f, &font_config, ranges);

    // Our state
    ImVec4 clear_color = ImVec4(0.20f, 0.20f, 0.20f, 1.00f);

    // Main loop
    bool done = false;
    while (!done)
    {
        // Poll and handle messages (inputs, window resize, etc.)
        // See the WndProc() function below for our to dispatch events to the Win32 backend.
        MSG msg;
        while (::PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE))
        {
            ::TranslateMessage(&msg);
            ::DispatchMessage(&msg);
            if (msg.message == WM_QUIT)
                done = true;
        }
        if (done)
            break;

        // Start the Dear ImGui frame
        ImGui_ImplDX9_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        static heads tab{ aim };
        static aim_sub_heads aim_subtab{ aimbot };
        static player_sub_heads player_subtab{ player_general };
        static visuals_sub_heads visuals_subtab{ players };
        static misc_sub_heads misc_subtab{ lists };

        // Aim variables
        static bool aimbot_enabled = false;
        static bool target_peds = false;
        static bool visible_check = false;
        static float smooth = 50.0f;
        static float curve = 50.0f;
        static float aim_distance = 100.0f;
        static bool hitbox = false;
        static bool dual_aimbot = false;
        static bool fov_circle = false;

        static bool triggerbot_enabled = false;
        static bool use_hotkey = false;
        static bool target_players = false;
        static bool target_peds_trig = false;
        static bool target_dead = false;
        static float delay = 50.0f;

        static bool silent_aim_enabled = false;
        static bool enable_silent_aim = false;
        static float silent_fov = 50.0f;
        static bool silent_hitbox = false;
        static bool silent_fov_circle = false;

        static bool magic_bullet_enabled = false;
        static bool enable_magic_bullet = false;
        static float magic_fov = 50.0f;
        static bool magic_hitbox = false;
        static bool magic_fov_circle = false;
        static ImVec4 fov_color = ImVec4(0.0f, 1.0f, 1.0f, 1.0f);

        const char* tab_name = tab == aim ? "Aim" : tab == player ? "Player" : tab == visuals ? "Visuals" : tab == miscellaneous ? "Miscellaneous" : tab == configs ? "Configs" : 0;
        const char* tab_icon = tab == aim ? "B" : tab == player ? "C" : tab == visuals ? "D" : tab == miscellaneous ? "E" : tab == configs ? "F" : 0;

        static bool boolean, boolean_1 = false;
        static int sliderscalar, combo = 0;

        const char* combo_items[3] = { "Value", "Value 1", "Value 2" };

        ImGui::SetNextWindowSize({ 730, 460 });
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, { 0, 0 });

        ImGui::Begin("hi world", nullptr, ImGuiWindowFlags_NoDecoration); {
            auto draw = ImGui::GetWindowDrawList();

            auto pos = ImGui::GetWindowPos();
            auto size = ImGui::GetWindowSize();

            ImGuiStyle style = ImGui::GetStyle();

            draw->AddRectFilled(pos, ImVec2(pos.x + 210, pos.y + size.y), ImColor(24, 24, 26), style.WindowRounding, ImDrawFlags_RoundCornersLeft);
            draw->AddLine(ImVec2(pos.x + 210, pos.y + 2), ImVec2(pos.x + 210, pos.y + size.y - 2), ImColor(1.0f, 1.0f, 1.0f, 0.03f));
            draw->AddLine(ImVec2(pos.x + 47, pos.y + 2), ImVec2(pos.x + 47, pos.y + size.y - 2), ImColor(1.0f, 1.0f, 1.0f, 0.03f));
            draw->AddLine(ImVec2(pos.x + 2, pos.y + 47), ImVec2(pos.x + 47, pos.y + 47), ImColor(1.0f, 1.0f, 1.0f, 0.03f));
            draw->AddLine(ImVec2(pos.x + 63, pos.y + 47), ImVec2(pos.x + 195, pos.y + 47), ImColor(1.0f, 1.0f, 1.0f, 0.03f));
            draw->AddText(logo, 21.0f, ImVec2(pos.x + 14, pos.y + 12), ImColor(147, 190, 66), "A");

            draw->AddText(tab_title_icon, 18.0f, ImVec2(pos.x + 65, pos.y + 14), ImColor(147, 190, 66), tab_icon);
            draw->AddText(tab_title, 19.0f, ImVec2(pos.x + 93, pos.y + 15), ImColor(1.0f, 1.0f, 1.0f), tab_name);

            draw->AddRect(pos + ImVec2(1, 1), pos + size - ImVec2(1, 1), ImColor(1.0f, 1.0f, 1.0f, 0.03f), style.WindowRounding);

            ImGui::SetCursorPos({ 8, 56 });
            ImGui::BeginGroup(); {
                if (elements::tab("B", tab == aim)) { tab = aim; }
                if (elements::tab("C", tab == player)) { tab = player; }
                if (elements::tab("D", tab == visuals)) { tab = visuals; }
                if (elements::tab("E", tab == miscellaneous)) { tab = miscellaneous; }
                if (elements::tab("F", tab == configs)) { tab = configs; }
            } ImGui::EndGroup();

            switch (tab) {
            case aim:
                draw->AddText(subtab_title, 15.0f, ImVec2(pos.x + 72, pos.y + 60), ImColor(1.0f, 1.0f, 1.0f, 0.4f), "AIM");

                ImGui::SetCursorPos({ 57, 86 });
                ImGui::BeginGroup(); {
                    if (elements::subtab("Aimbot", aim_subtab == aimbot)) { aim_subtab = aimbot; }
                    if (elements::subtab("Triggerbot", aim_subtab == triggerbot)) { aim_subtab = triggerbot; }
                    if (elements::subtab("Silent Aim", aim_subtab == silent_aim)) { aim_subtab = silent_aim; }
                    if (elements::subtab("Magic Bullet", aim_subtab == magic_bullet)) { aim_subtab = magic_bullet; }
                } ImGui::EndGroup();

                switch (aim_subtab) {
                case aimbot:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Aimbot Settings", ImVec2(240, 200)); {
                        ImGui::Checkbox("Aimbot", &aimbot_enabled);
                        ImGui::Checkbox("Target Peds", &target_peds);
                        ImGui::Checkbox("Visible Check", &visible_check);
                        ImGui::SliderFloat("Smooth", &smooth, 0.0f, 100.0f, "%.1f");
                        ImGui::SliderFloat("Curve", &curve, 0.0f, 100.0f, "%.1f");
                        ImGui::SliderFloat("Aim Distance", &aim_distance, 0.0f, 500.0f, "%.1f");
                        ImGui::Checkbox("Hitbox", &hitbox);
                        ImGui::Checkbox("Dual Aimbot", &dual_aimbot);
                        ImGui::Checkbox("FOV Circle", &fov_circle);
                    }
                    e_elements::end_child();
                    break;

                case triggerbot:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Triggerbot Settings", ImVec2(240, 200)); {
                        ImGui::Checkbox("Triggerbot", &triggerbot_enabled);
                        ImGui::Checkbox("Use Hotkey", &use_hotkey);
                        ImGui::Checkbox("Target Players", &target_players);
                        ImGui::Checkbox("Target Peds", &target_peds_trig);
                        ImGui::Checkbox("Target Dead", &target_dead);
                        ImGui::SliderFloat("Delay", &delay, 0.0f, 1000.0f, "%.1f ms");
                    }
                    e_elements::end_child();
                    break;

                case silent_aim:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Silent Aim Settings", ImVec2(240, 200)); {
                        ImGui::Checkbox("Silent Aim", &silent_aim_enabled);
                        ImGui::Checkbox("Enable Silent Aim", &enable_silent_aim);
                        ImGui::SliderFloat("Field Of View", &silent_fov, 0.0f, 180.0f, "%.1f");
                        ImGui::Checkbox("Hitbox", &silent_hitbox);
                        ImGui::Checkbox("FOV Circle", &silent_fov_circle);
                    }
                    e_elements::end_child();
                    break;

                case magic_bullet:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Magic Bullet Settings", ImVec2(240, 200)); {
                        ImGui::Checkbox("Magic Bullet", &magic_bullet_enabled);
                        ImGui::Checkbox("Enable Magic Bullet", &enable_magic_bullet);
                        ImGui::SliderFloat("Field Of View", &magic_fov, 0.0f, 180.0f, "%.1f");
                        ImGui::Checkbox("Hitbox", &magic_hitbox);
                        ImGui::Checkbox("FOV Circle", &magic_fov_circle);
                        ImGui::ColorEdit4("FOV Color", (float*)&fov_color);
                    }
                    e_elements::end_child();
                    break;
                }
                break;

            case player:
                draw->AddText(subtab_title, 15.0f, ImVec2(pos.x + 72, pos.y + 60), ImColor(1.0f, 1.0f, 1.0f, 0.4f), "PLAYER");

                ImGui::SetCursorPos({ 57, 86 });
                ImGui::BeginGroup(); {
                    if (elements::subtab("Player", player_subtab == player_general)) { player_subtab = player_general; }
                } ImGui::EndGroup();

                switch (player_subtab) {
                case player_general:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Player Settings", ImVec2(240, 300)); {
                        ImGui::Text("Player options will be implemented here");
                        // Add player-specific options here
                    }
                    e_elements::end_child();
                    break;
                }
                break;

            case visuals:
                draw->AddText(subtab_title, 15.0f, ImVec2(pos.x + 72, pos.y + 60), ImColor(1.0f, 1.0f, 1.0f, 0.4f), "VISUALS");

                ImGui::SetCursorPos({ 57, 86 });
                ImGui::BeginGroup(); {
                    if (elements::subtab("Players", visuals_subtab == players)) { visuals_subtab = players; }
                    if (elements::subtab("World", visuals_subtab == world)) { visuals_subtab = world; }
                } ImGui::EndGroup();

                switch (visuals_subtab) {
                case players:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Player ESP", ImVec2(240, 300)); {
                        ImGui::Text("Player ESP options will be implemented here");
                        // Add ESP options here
                    }
                    e_elements::end_child();
                    break;

                case world:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("World ESP", ImVec2(240, 300)); {
                        ImGui::Text("World ESP options will be implemented here");
                        // Add world ESP options here
                    }
                    e_elements::end_child();
                    break;
                }
                break;

            case miscellaneous:
                draw->AddText(subtab_title, 15.0f, ImVec2(pos.x + 72, pos.y + 60), ImColor(1.0f, 1.0f, 1.0f, 0.4f), "MISC");

                ImGui::SetCursorPos({ 57, 86 });
                ImGui::BeginGroup(); {
                    if (elements::subtab("Lists", misc_subtab == lists)) { misc_subtab = lists; }
                    if (elements::subtab("Miscellaneous", misc_subtab == miscellaneous_general)) { misc_subtab = miscellaneous_general; }
                } ImGui::EndGroup();

                switch (misc_subtab) {
                case lists:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Lists", ImVec2(240, 300)); {
                        ImGui::Text("Lists options will be implemented here");
                        // Add lists options here
                    }
                    e_elements::end_child();
                    break;

                case miscellaneous_general:
                    ImGui::SetCursorPos({ 226, 16 });
                    e_elements::begin_child("Miscellaneous", ImVec2(240, 300)); {
                        ImGui::Text("Miscellaneous options will be implemented here");
                        // Add misc options here
                    }
                    e_elements::end_child();
                    break;
                }
                break;

            case configs:
                draw->AddText(subtab_title, 15.0f, ImVec2(pos.x + 72, pos.y + 60), ImColor(1.0f, 1.0f, 1.0f, 0.4f), "CONFIGS");

                ImGui::SetCursorPos({ 226, 16 });
                e_elements::begin_child("Config Management", ImVec2(240, 300)); {
                    ImGui::Text("Config management will be implemented here");
                    // Add config save/load options here
                }
                e_elements::end_child();
                break;
            }
        }
        ImGui::End();

        ImGui::PopStyleVar();

        // Rendering
        ImGui::EndFrame();
        g_pd3dDevice->SetRenderState(D3DRS_ZENABLE, FALSE);
        g_pd3dDevice->SetRenderState(D3DRS_ALPHABLENDENABLE, FALSE);
        g_pd3dDevice->SetRenderState(D3DRS_SCISSORTESTENABLE, FALSE);
        D3DCOLOR clear_col_dx = D3DCOLOR_RGBA((int)(clear_color.x*clear_color.w*255.0f), (int)(clear_color.y*clear_color.w*255.0f), (int)(clear_color.z*clear_color.w*255.0f), (int)(clear_color.w*255.0f));
        g_pd3dDevice->Clear(0, NULL, D3DCLEAR_TARGET | D3DCLEAR_ZBUFFER, clear_col_dx, 1.0f, 0);
        if (g_pd3dDevice->BeginScene() >= 0)
        {
            ImGui::Render();
            ImGui_ImplDX9_RenderDrawData(ImGui::GetDrawData());
            g_pd3dDevice->EndScene();
        }
        HRESULT result = g_pd3dDevice->Present(NULL, NULL, NULL, NULL);

        // Handle loss of D3D9 device
        if (result == D3DERR_DEVICELOST && g_pd3dDevice->TestCooperativeLevel() == D3DERR_DEVICENOTRESET)
            ResetDevice();
    }

    ImGui_ImplDX9_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    ::DestroyWindow(hwnd);
    ::UnregisterClass(wc.lpszClassName, wc.hInstance);

    return 0;
}

// Helper functions

bool CreateDeviceD3D(HWND hWnd)
{
    if ((g_pD3D = Direct3DCreate9(D3D_SDK_VERSION)) == NULL)
        return false;

    // Create the D3DDevice
    ZeroMemory(&g_d3dpp, sizeof(g_d3dpp));
    g_d3dpp.Windowed = TRUE;
    g_d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;
    g_d3dpp.BackBufferFormat = D3DFMT_UNKNOWN; // Need to use an explicit format with alpha if needing per-pixel alpha composition.
    g_d3dpp.EnableAutoDepthStencil = TRUE;
    g_d3dpp.AutoDepthStencilFormat = D3DFMT_D16;
    g_d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_ONE;           // Present with vsync
    //g_d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_IMMEDIATE;   // Present without vsync, maximum unthrottled framerate
    if (g_pD3D->CreateDevice(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, hWnd, D3DCREATE_HARDWARE_VERTEXPROCESSING, &g_d3dpp, &g_pd3dDevice) < 0)
        return false;

    return true;
}

void CleanupDeviceD3D()
{
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = NULL; }
    if (g_pD3D) { g_pD3D->Release(); g_pD3D = NULL; }
}

void ResetDevice()
{
    ImGui_ImplDX9_InvalidateDeviceObjects();
    HRESULT hr = g_pd3dDevice->Reset(&g_d3dpp);
    if (hr == D3DERR_INVALIDCALL)
        IM_ASSERT(0);
    ImGui_ImplDX9_CreateDeviceObjects();
}

// Forward declare message handler from imgui_impl_win32.cpp
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Win32 message handler
// You can read the io.WantCaptureMouse, io.WantCaptureKeyboard flags to tell if dear imgui wants to use your inputs.
// - When io.WantCaptureMouse is true, do not dispatch mouse input data to your main application, or clear/overwrite your copy of the mouse data.
// - When io.WantCaptureKeyboard is true, do not dispatch keyboard input data to your main application, or clear/overwrite your copy of the keyboard data.
// Generally you may always pass all inputs to dear imgui, and hide them from your application based on those two flags.
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg)
    {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED)
        {
            g_d3dpp.BackBufferWidth = LOWORD(lParam);
            g_d3dpp.BackBufferHeight = HIWORD(lParam);
            ResetDevice();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        ::PostQuitMessage(0);
        return 0;
    }
    return ::DefWindowProc(hWnd, msg, wParam, lParam);
}
